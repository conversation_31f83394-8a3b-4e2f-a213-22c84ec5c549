import io
import os
import dis
import math
import yarl
import typing
import random
import asyncio
import datetime

import discord
from collections import defaultdict
from discord.ext import commands, menus

from utilities import utils
from utilities import checks
from utilities import helpers
from utilities import converters
from utilities import decorators
from utilities import formatting
from utilities import pagination
from utilities.noprefix import duration_to_timedelta, format_expiry_time
from discord.ext import tasks


async def setup(bot):
    await bot.add_cog(Botadmin(bot))


class GithubError(commands.CommandError):
    pass


class DurationSelect(discord.ui.Select):
    """Select menu for choosing no-prefix duration"""

    def __init__(self, user: discord.User, author: discord.User):
        super().__init__(placeholder="Select the duration")
        self.user = user
        self.author = author

        self.options = [
            discord.SelectOption(label="10 Minutes", description="Trial for 10 minutes", value="10m"),
            discord.SelectOption(label="1 Hour", description="No prefix for 1 hour", value="1h"),
            discord.SelectOption(label="1 Day", description="No prefix for 1 day", value="1d"),
            discord.SelectOption(label="1 Week", description="No prefix for 1 week", value="1w"),
            discord.SelectOption(label="2 Weeks", description="No prefix for 2 weeks", value="2w"),
            discord.SelectOption(label="1 Month", description="No prefix for 1 month", value="1M"),
            discord.SelectOption(label="3 Months", description="No prefix for 3 months", value="3M"),
            discord.SelectOption(label="6 Months", description="No prefix for 6 months", value="6M"),
            discord.SelectOption(label="1 Year", description="No prefix for 1 year", value="1y"),
            discord.SelectOption(label="Lifetime", description="No prefix permanently", value="lifetime"),
        ]

    async def callback(self, interaction: discord.Interaction):
        if interaction.user != self.author:
            return await interaction.response.send_message("You can't select this option.", ephemeral=True)

        selected_duration = self.values[0]
        expiry_time = None

        if selected_duration != "lifetime":
            duration = duration_to_timedelta(selected_duration)
            if duration:
                expiry_time = datetime.datetime.utcnow() + duration

        # Add user to no-prefix
        success = await interaction.client.noprefix_manager.add_user(
            self.user.id, expiry_time, self.author.id
        )

        if not success:
            embed = discord.Embed(
                title="Error",
                description="Failed to add user to no-prefix list. Please try again.",
                color=0xFF0000
            )
            return await interaction.response.edit_message(embed=embed, view=None)

        # Format expiry display
        expiry_display, expiry_timestamp = format_expiry_time(expiry_time)

        embed = discord.Embed(
            title="✅ Added No Prefix",
            description=(
                f"**User**: {self.user.mention} (`{self.user.id}`)\n"
                f"**Added By**: {self.author.mention}\n"
                f"**Duration**: {selected_duration.upper()}\n"
                f"**Expires**: {expiry_display}"
            ),
            color=0x00FF00
        )
        embed.set_thumbnail(url=self.user.display_avatar.url)

        await interaction.response.edit_message(embed=embed, view=None)

        # Log the action
        print(f"[NoPrefix] {self.author} ({self.author.id}) added {self.user} ({self.user.id}) to no-prefix list. Duration: {selected_duration}, Expires: {expiry_display}")

        # Send DM to user
        try:
            dm_embed = discord.Embed(
                title="🎉 No Prefix Granted!",
                description=(
                    f"You have been granted no-prefix permissions! "
                    f"You can now use bot commands without any prefix.\n\n"
                    f"**Duration**: {selected_duration.upper()}\n"
                    f"**Expires**: {expiry_display}"
                ),
                color=0x00FF00
            )
            dm_embed.set_footer(text="Enjoy your no-prefix experience!")
            await self.user.send(embed=dm_embed)
        except (discord.Forbidden, discord.HTTPException):
            pass  # User has DMs disabled


class DurationSelectView(discord.ui.View):
    """View containing the duration select menu"""

    def __init__(self, user: discord.User, author: discord.User):
        super().__init__(timeout=300)
        self.add_item(DurationSelect(user, author))


class Botadmin(commands.Cog):
    """
    Bot admin only stats cog.
    """

    def __init__(self, bot):
        self.bot = bot
        self._req_lock = asyncio.Lock()

        self.torment = False

        # Start the noprefix cleanup task
        self.noprefix_cleanup.start()

    def cog_unload(self):
        self.noprefix_cleanup.cancel()

    # This is a bot admin only cog
    async def cog_check(self, ctx):
        if checks.is_admin(ctx):
            return True
        return

    @tasks.loop(minutes=10)
    async def noprefix_cleanup(self):
        """Check for expired no-prefix users and remove them"""
        if not self.bot.ready or not hasattr(self.bot, 'noprefix_manager'):
            return

        try:
            expired_users = await self.bot.noprefix_manager.cleanup_expired()

            if expired_users:
                print(f"[NoPrefix] Cleaned up {len(expired_users)} expired no-prefix users: {expired_users}")

            for user_id in expired_users:
                user = self.bot.get_user(user_id)
                if user:
                    print(f"[NoPrefix] No-prefix expired for {user} ({user.id})")
                    try:
                        embed = discord.Embed(
                            title="⚠️ No Prefix Expired",
                            description=(
                                f"Your no-prefix permissions have expired. "
                                f"You will now need to use a prefix to run commands."
                            ),
                            color=0xFFAA00
                        )
                        embed.set_footer(text="Contact support if you believe this is an error")
                        await user.send(embed=embed)
                    except (discord.Forbidden, discord.HTTPException):
                        print(f"[NoPrefix] Failed to send expiry DM to {user} ({user.id})")
                else:
                    print(f"[NoPrefix] No-prefix expired for unknown user ({user_id})")
        except Exception as e:
            print(f"[NoPrefix] Error during cleanup: {e}")

    @noprefix_cleanup.before_loop
    async def before_noprefix_cleanup(self):
        await self.bot.wait_until_ready()

    async def github_request(
        self, method, url, *, params=None, data=None, headers=None
    ):
        hdrs = {
            "Accept": "application/vnd.github.inertia-preview+json",
            "User-Agent": "Hina Gist Creator",
            "Authorization": f"token {self.bot.config.KEYS.github}",
        }

        req_url = yarl.URL("https://api.github.com") / url

        if headers is not None and isinstance(headers, dict):
            hdrs.update(headers)

        await self._req_lock.acquire()
        try:
            async with self.bot.session.request(
                method, req_url, params=params, json=data, headers=hdrs
            ) as r:
                remaining = r.headers.get("X-Ratelimit-Remaining")
                js = await r.json()
                if r.status == 429 or remaining == "0":
                    # wait before we release the lock
                    delta = discord.utils._parse_ratelimit_header(r)
                    await asyncio.sleep(delta)
                    self._req_lock.release()
                    return await self.github_request(
                        method, url, params=params, data=data, headers=headers
                    )
                elif 300 > r.status >= 200:
                    return js
                else:
                    raise GithubError(js["message"])
        finally:
            if self._req_lock.locked():
                self._req_lock.release()

    async def create_gist(
        self, content, *, description=None, filename=None, public=True
    ):
        headers = {
            "Accept": "application/vnd.github.v3+json",
        }

        filename = filename or "output.txt"
        data = {"public": public, "files": {filename: {"content": content}}}

        if description:
            data["description"] = description

        js = await self.github_request("POST", "gists", data=data, headers=headers)
        return js["html_url"]

    @decorators.command(
        aliases=["torment"],
        brief="Retarded",
    )
    async def annoy(
        self,
        ctx,
        members: commands.Greedy[converters.DiscordMember(False)],
        times: int = 10,
    ):
        """What retard needs help for this?"""

        # Set the torment flag
        self.torment = True

        if times > 100:
            times = 100

        if times < 1 or not members:
            await ctx.reply("retard")
            return

        # Delete original torment message
        try:
            await ctx.message.delete()
        except:
            pass

        for i in range(0, times):
            for channel in ctx.guild.channels:
                try:
                    await channel.send(
                        " ".join(
                            [
                                m.mention
                                for m in members
                                if channel.permissions_for(m).read_messages
                            ]
                        ),
                        delete_after=0,
                    )
                except Exception:
                    continue
            await asyncio.sleep(1)
            if not self.torment:
                break

    @decorators.command(
        aliases=["canceltorment"],
        brief="This is retarded",
    )
    async def cancelannoy(self, ctx):
        """What retard needs help for this?"""

        self.torment = False
        await ctx.send("retarded.")

    @decorators.command(
        aliases=["rawhelp"],
        brief="Default",
    )
    async def defaulthelp(self, ctx, *, search: str = None):
        if not search:
            return await ctx.send_help()
        await ctx.send_help(search)

    @decorators.command(
        aliases=["helpless"],
        brief="Show helpless commands.",
        implemented="2021-04-14 00:54:20.465452",
        updated="2021-05-19 16:00:16.754845",
    )
    async def debug(self, ctx):
        """
        Usage: {0}debug
        Alias: {0}helpless
        Output:
            Shows all commands that lack
            descriptions or help.
        """
        await ctx.trigger_typing()
        cogs = []
        for cog in self.bot.cogs:
            cog = self.bot.get_cog(cog)
            cogs.append(cog)

        msg = "#HELPLESS COMMANDS\n"
        for cog in cogs:
            if cog.qualified_name == "Testing":
                continue
            for cmd in cog.get_commands():
                if cmd.help is None:
                    msg += cmd.name + "\n"

        msg2 = "#BRIEFLESS COMMANDS\n"
        for cog in cogs:
            if cog.qualified_name == "Testing":
                continue
            for cmd in cog.get_commands():
                if cmd.brief is None or cmd.brief == "":
                    msg2 += cmd.name + "\n"
        msg = msg + "\n" + msg2
        import io

        data = io.BytesIO(msg.encode("utf-8"))
        await ctx.send_or_reply(
            file=discord.File(data, filename="helpless-commands.md")
        )

    @decorators.command(
        aliases=["dumpguilds", "txtguilds", "txtservers"],
        brief="DMs all servers.",
        implemented="2021-04-09 02:05:49.278468",
        updated="2021-05-06 15:57:20.290636",
    )
    @checks.is_bot_admin()
    async def dumpservers(self, ctx):
        """
        Usage: {0}dumpservers
        ALiases:
            {0}dumpguilds, {0]txtguilds, {0}txtservers
        Permission: Bot Admin
        Output:
            DMs you a file with all my servers,
            their member count, owners, and their IDs
        Notes:
            If you have your DMs blocked, the bot
            will send the file to the channel
            where the the command was invoked.
        """
        timestamp = discord.utils.utcnow().strftime("%Y-%m-%d %H.%M")
        server_file = "Servers-{}.txt".format(timestamp)

        mess = await ctx.load(
            content="Saving servers to **{}**...".format(server_file),
        )

        msg = ""
        for server in self.bot.guilds:
            msg += "Name:    " + server.name + "\n"
            msg += "ID:      " + str(server.id) + "\n"
            msg += "Owner:   " + str(server.owner) + "\n"
            msg += "Members: " + str(len(server.members)) + "\n"
            msg += "\n\n"

        data = io.BytesIO(msg.encode("utf-8"))

        await mess.edit(content="Uploading `{}`...".format(server_file))
        try:
            await ctx.author.send(file=discord.File(data, filename=server_file))
        except Exception:
            await ctx.send_or_reply(
                file=discord.File(data, filename=server_file),
            )
            await mess.edit(
                content="{} Uploaded `{}`.".format(
                    self.bot.emote_dict["success"], server_file
                )
            )
            return
        await mess.edit(
            content="{} Uploaded `{}`.".format(
                self.bot.emote_dict["success"], server_file
            )
        )
        await mess.add_reaction(self.bot.emote_dict["letter"])

    @decorators.command(
        brief="Post a gist on github.",
        implemented="2021-05-10 18:58:23.417218",
        updated="2021-05-19 15:53:27.603043",
    )
    async def gist(self, ctx, fname="output.txt", *, content=None):
        """
        Usage: {0}gist [filename=output.txt] [content]
        Output:
            Creates a gist using your posted content
        Notes:
            Will post a gist of the first message attachment
            with it's file extension if no content is passed.
        """
        if len(ctx.message.attachments):
            f = await ctx.message.attachments[0].read()
            content = f.decode("utf-8")
            fname = ctx.message.attachments[0].filename
        else:
            if not content:
                return await ctx.usage(ctx.command.signature)

        url = await self.create_gist(
            content,
            description=f"Uploaded by {ctx.author} ({ctx.author.id}) at {discord.utils.utcnow()}",
            filename=fname,
        )
        await ctx.reply(f"<:pepeLaugh:779433733400166420> <{url}>")

    @decorators.command(
        name="message",
        aliases=["pm", "dm"],
        brief="DM a user the bot knows.",
    )
    async def _message(self, ctx, user: converters.DiscordUser, *, message: str):
        """
        Usage:       {0}message <user> <message>
        Alias:       {0}pm, {0}dm
        Example:     {0}dm *************** Hi
        Permissions: Bot Owner
        """
        try:
            await user.send(message, files=ctx.message.attachments)
            await ctx.send_or_reply(
                content=f"{self.bot.emote_dict['letter']} Sent a DM to **{user}**",
            )
        except discord.Forbidden:
            await ctx.send_or_reply(
                "This user might be having DMs blocked or it's a bot account..."
            )

    @decorators.command(brief="Create a server invite.")
    async def inv(self, ctx, server: converters.BotServer = None):
        """
        Usage: {0}inv <server>
        Output: Invite for that server (if bot could create it)
        """
        if server is None:
            server = ctx.guild
        if isinstance(server, list):
            if len(server) > 1:
                msg = ""
                for x in server:
                    guild = self.bot.get_guild(int(x))
                    msg += f"ID: {guild.id} Name: {guild.name}\n"
                await ctx.send_or_reply(
                    content=f"Multiple results. Please use the server ID instead",
                )
                t = pagination.MainMenu(
                    pagination.TextPageSource(text=msg, max_size=500)
                )
                try:
                    return await t.start(ctx)
                except menus.MenuError as e:
                    await ctx.send_or_reply(e)
            else:
                try:
                    server = self.bot.get_guild(int(server[0]))
                except IndexError:
                    return await ctx.send_or_reply(
                        content=f"Couldn't find that server.",
                    )

        s = random.choice(server.text_channels)
        try:
            inv = await s.create_invite()
        except Exception as e:
            return await ctx.send_or_reply(e)
        await ctx.send_or_reply(inv)

    @decorators.command(brief="Lists all servers", aliases=["servers", "serverlist"])
    async def listservers(self, ctx):
        """
        Usage: {0}listservers
        Alias: {0}servers, {0}serverlist
        Output: Lists the servers I'm connected to.
        """
        our_list = []
        for guild in self.bot.guilds:
            our_list.append(
                {
                    "name": guild.name,
                    "value": "{:,} member{}\nID: `{}`".format(
                        len(guild.members),
                        "" if len(guild.members) == 1 else "s",
                        guild.id,
                    ),
                    "users": len(guild.members),
                }
            )
        p = pagination.MainMenu(
            pagination.FieldPageSource(
                entries=[
                    ("{}. {}".format(y + 1, x["name"]), x["value"])
                    for y, x in enumerate(our_list)
                ],
                title="Server's I'm Connected To ({:,} total)".format(
                    len(self.bot.guilds)
                ),
                per_page=15,
            )
        )

        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    @decorators.command(brief="Show most member servers.")
    async def topservers(self, ctx):
        """
        Usage: {0}topservers
        Output: The servers with the most memebers
        """
        our_list = []
        for guild in self.bot.guilds:
            our_list.append(
                {
                    "name": guild.name,
                    "value": "{:,} member{}".format(
                        len(guild.members), "" if len(guild.members) == 1 else "s"
                    ),
                    "users": len(guild.members),
                }
            )
        our_list = sorted(our_list, key=lambda x: x["users"], reverse=True)
        p = pagination.MainMenu(
            pagination.FieldPageSource(
                entries=[
                    ("{}. {}".format(y + 1, x["name"]), x["value"])
                    for y, x in enumerate(our_list)
                ],
                title="Top Servers By Population ({} total)".format(
                    len(self.bot.guilds)
                ),
                per_page=15,
            )
        )

        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    @decorators.command(brief="Show least member servers.")
    async def bottomservers(self, ctx):
        """
        Usage: {0}bottomservers
        Output: The servers with the least memebers
        """
        our_list = []
        for guild in self.bot.guilds:
            our_list.append(
                {
                    "name": guild.name,
                    "value": "{:,} member{}".format(
                        len(guild.members), "" if len(guild.members) == 1 else "s"
                    ),
                    "users": len(guild.members),
                }
            )
        our_list = sorted(our_list, key=lambda x: x["users"])
        p = pagination.MainMenu(
            pagination.FieldPageSource(
                entries=[
                    ("{}. {}".format(y + 1, x["name"]), x["value"])
                    for y, x in enumerate(our_list)
                ],
                title="Bottom Servers By Population ({:,} total)".format(
                    len(self.bot.guilds)
                ),
                per_page=15,
            )
        )

        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    @decorators.command(brief="Show first joined servers.")
    async def firstservers(self, ctx):
        """
        Usage: {0}firstservers
        Output: Lists the first servers I joined
        """
        our_list = []
        for guild in self.bot.guilds:
            bot = guild.me
            our_list.append(
                {
                    "name": "{} ({:,} member{})".format(
                        guild.name,
                        len(guild.members),
                        "" if len(guild.members) == 1 else "s",
                    ),
                    "value": "{} UTC".format(
                        bot.joined_at.strftime("%Y-%m-%d %I:%M %p")
                        if bot.joined_at is not None
                        else "Unknown"
                    ),
                    "date": bot.joined_at,
                }
            )
        our_list = sorted(
            our_list,
            key=lambda x: x["date"].timestamp() if x["date"] is not None else -1,
        )
        p = pagination.MainMenu(
            pagination.FieldPageSource(
                entries=[
                    ("{}. {}".format(y + 1, x["name"]), x["value"])
                    for y, x in enumerate(our_list)
                ],
                title="First Servers I Joined ({:,} total)".format(
                    len(self.bot.guilds)
                ),
                per_page=15,
            )
        )

        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    @decorators.command(brief="Show latest joined servers.", aliases=["lastservers"])
    async def recentservers(self, ctx):
        """
        Usage: {0}recentservers
        Alias: {0}lastservers
        Output: Lists the most recent servers joined
        """
        our_list = []
        for guild in self.bot.guilds:
            bot = guild.me
            our_list.append(
                {
                    "name": "{} ({} member{})".format(
                        guild.name,
                        len(guild.members),
                        "" if len(guild.members) == 1 else "s",
                    ),
                    "value": "{} UTC".format(
                        bot.joined_at.strftime("%Y-%m-%d %I:%M %p")
                        if bot.joined_at is not None
                        else "Unknown"
                    ),
                    "date": bot.joined_at,
                }
            )
        our_list = sorted(
            our_list,
            key=lambda x: x["date"].timestamp() if x["date"] is not None else -1,
            reverse=True,
        )
        p = pagination.MainMenu(
            pagination.FieldPageSource(
                entries=[
                    ("{}. {}".format(y + 1, x["name"]), x["value"])
                    for y, x in enumerate(our_list)
                ],
                title="Most Recent Servers I Joined ({:,} total)".format(
                    len(self.bot.guilds)
                ),
                per_page=15,
            )
        )

        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    # Basic info commands
    @decorators.command(brief="Show commands in the cache.")
    async def cachedcommands(self, ctx, limit=20):
        """
        Usage: {0}cachedcommands
        Output:
            Show the commands in the bot's cache
        """
        counter = self.bot.command_stats
        width = len(max(counter, key=len))

        if limit > 0:
            common = counter.most_common(limit)
        else:
            common = counter.most_common()[limit:]
        output = "\n".join("{0:<{1}} : {2}".format(k, width, c) for k, c in common)

        p = pagination.MainMenu(pagination.TextPageSource(output, prefix="```yaml"))
        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    @decorators.command(aliases=["ns"], brief="List all bot nicknames.")
    async def nickscan(self, ctx):
        """
        Usage: {0}nickscan
        Alias: {0}ns
        Output:
            All my nicknames across all servers
        """
        nick = ""
        bool = None
        for guild in self.bot.guilds:
            if (
                len(
                    nick
                    + "**Server:** `{}` **Nick:** `{}`\n".format(
                        guild.name, guild.get_member(self.bot.user.id).nick
                    )
                )
                > 2000
            ):
                bool = False
                break
            if guild.get_member(self.bot.user.id).nick:
                nick += "**Server:** `{}` **Nick:** `{}`\n".format(
                    guild.name, guild.get_member(self.bot.user.id).nick
                )
        if not nick:
            await ctx.send_or_reply(content="I have no nicknames set!")
        else:
            if len(nick) <= 1964 and bool is False:
                nick += "**Could not print the rest, sorry.**"
            elif bool is False:
                bool = True
            try:
                embed = discord.Embed(
                    title="Servers I Have Nicknames In",
                    color=self.bot.mode.EMBED_COLOR,
                )
                embed.description = nick
                await ctx.send_or_reply(embed=embed)
            except BaseException:
                await ctx.send_or_reply(content="```{}```".format(nick))
            if bool is True:
                await ctx.send_or_reply(
                    content="**Could not print the rest, sorry.**",
                )

    def _get_imports(self, file_name):
        if not os.path.exists("cogs/" + file_name):
            return []
        file_string = open("cogs/" + file_name, "rb").read().decode("utf-8")
        instructions = dis.get_instructions(file_string)
        imports = [__ for __ in instructions if "IMPORT" in __.opname]
        i = []
        for instr in imports:
            if not instr.opname == "IMPORT_FROM":
                continue
            i.append(instr.argval)
        cog_imports = []
        for f in i:
            if os.path.exists("cogs/" + f + ".py"):
                cog_imports.append(f)
        return cog_imports

    def _get_imported_by(self, file_name):
        ext_list = []
        for ext in os.listdir("cogs"):
            # Avoid reloading Settings and Mute
            if not ext.lower().endswith(".py") or ext == file_name:
                continue
            if file_name[:-3] in self._get_imports(ext):
                ext_list.append(ext)
        return ext_list

    async def _send_embed(self, ctx, embed, pm=False):
        # Helper method to send embeds to their proper location
        if pm is True and not ctx.channel == ctx.author.dm_channel:
            # More than 2 pages, try to dm
            try:
                await ctx.send_or_reply(embed=embed)
                # await ctx.author.send(embed=embed)
                # await ctx.message.add_reaction("📬")
            except discord.Forbidden:
                await ctx.send_or_reply(embed=embed)
            return
        await ctx.send_or_reply(embed=embed)

    def _is_submodule(self, parent, child):
        return parent == child or child.startswith(parent + ".")

    @decorators.command(brief="Show info on an extension.", aliases=["ext"])
    async def extension(self, ctx, *, extension=None):
        """
        Usage: {0}extension <extension>
        Alias: {0}ext
        Outputs the cogs attatched to the passed extension.
        """
        if extension is None:
            # run the extensions command
            await ctx.invoke(self.extensions)
            return

        cog_list = []
        for e in self.bot.extensions:
            if not str(e[5:]).lower() == extension.lower():
                continue
            # At this point - we should've found it
            # Get the extension
            b_ext = self.bot.extensions.get(e)
            for cog in self.bot.cogs:
                # Get the cog
                b_cog = self.bot.get_cog(cog)
                if self._is_submodule(b_ext.__name__, b_cog.__module__):
                    # Submodule - add it to the list
                    cog_list.append(str(cog))
            # build the embed
            help_embed = discord.Embed(color=self.bot.mode.EMBED_COLOR)

            help_embed.title = str(e[5:]) + ".py" + " Extension"
            if len(cog_list):
                total_commands = 0
                total_listeners = 0
                for cog in cog_list:
                    total_commands += len(self.bot.get_cog(cog).get_commands())
                    total_listeners += len(self.bot.get_cog(cog).get_listeners())
                if len(cog_list) > 1:
                    comm = "total command"
                    event = "total event"
                else:
                    comm = "command"
                    event = "event"
                if total_commands == 1:
                    comm = "> 1 " + comm
                else:
                    comm = "> {:,} {}s".format(total_commands, comm)
                if total_listeners == 1:
                    event = "> 1 " + event
                else:
                    event = "> {:,} {}s".format(total_listeners, event)
                help_embed.add_field(name=", ".join(cog_list), value=comm, inline=True)
                help_embed.add_field(name=", ".join(cog_list), value=event, inline=True)
            else:
                help_embed.add_field(name="No Cogs", value="> 0 commands", inline=True)
            await ctx.send_or_reply(embed=help_embed)
            return
        await ctx.send_or_reply(content="I couldn't find that extension.")

    @decorators.group(
        name="self",
        brief="Owner self commands.",
        invoke_without_command=True,
    )
    @commands.is_owner()
    async def self(self, ctx):
        await ctx.send_or_reply("Use a subcommand: `prefix`")

    @self.command(
        name="prefix",
        brief="Set your own custom command prefix across all servers.",
    )
    async def prefix(self, ctx, *, prefix: str):
        """
        Usage: self prefix <prefix>
        Output:
            Sets your own custom command prefix across all servers.
        Notes:
            This prefix will only apply to commands you invoke.
        """
        await self.bot.cxn.execute(
            """
            INSERT INTO owner_prefixes (owner_id, prefix)
            VALUES ($1, $2)
            ON CONFLICT (owner_id) DO UPDATE SET prefix = $2;
            """,
            ctx.author.id,
            prefix,
        )
        if not hasattr(self.bot, "owner_prefixes"):
            self.bot.owner_prefixes = {}
        self.bot.owner_prefixes[ctx.author.id] = prefix
        await ctx.success(f"Set your personal prefix to `{prefix}` across all servers.")

    @decorators.command(brief="List all cogs.", aliases=["exts"])
    async def extensions(self, ctx):
        """
        Usage: {0}extensions
        Alias: {0}exts
        Output: Lists all extensions and their corresponding cogs.
        """
        # Build the embed
        if isinstance(ctx.author, discord.Member):
            help_embed = discord.Embed(color=self.bot.mode.EMBED_COLOR)
        else:
            help_embed = discord.Embed(color=random.choice(self.colors))

        # Setup blank dict
        ext_list = {}
        cog_less = []
        for extension in self.bot.extensions:
            if not str(extension)[5:] in ext_list:
                ext_list[str(extension)[5:]] = []
            # Get the extension
            b_ext = self.bot.extensions.get(extension)
            for cog in self.bot.cogs:
                # Get the cog
                b_cog = self.bot.get_cog(cog)
                if self._is_submodule(b_ext.__name__, b_cog.__module__):
                    # Submodule - add it to the list
                    ext_list[str(extension)[5:]].append(str(cog))
            if not len(ext_list[str(extension)[5:]]):
                ext_list.pop(str(extension)[5:])
                cog_less.append(str(extension)[5:])

        if not len(ext_list) and not len(cog_less):
            # no extensions - somehow... just return
            return

        # Get all keys and sort them
        key_list = list(ext_list.keys())
        key_list = sorted(key_list)

        if len(cog_less):
            ext_list["Cogless"] = cog_less
            # add the cogless extensions at the end
            key_list.append("Cogless")

        to_pm = len(ext_list) > 24
        page_count = 1
        page_total = math.ceil(len(ext_list) / 24)
        if page_total > 1:
            help_embed.title = "Extensions (Page {:,} of {:,})".format(
                page_count, page_total
            )
        else:
            help_embed.title = "Extensions"
        for embed in key_list:
            if len(ext_list[embed]):
                help_embed.add_field(
                    name=embed, value="> " + ", ".join(ext_list[embed]), inline=True
                )
            else:
                help_embed.add_field(name=embed, value="> None", inline=True)
            # 25 field max - send the embed if we get there
            if len(help_embed.fields) >= 24:
                if page_total == page_count:
                    if len(ext_list) == 1:
                        help_embed.set_footer(text="1 Extension Total")
                    else:
                        help_embed.set_footer(
                            text="{} Extensions Total".format(len(ext_list))
                        )
                await self._send_embed(ctx, help_embed, to_pm)
                help_embed.clear_fields()
                page_count += 1
                if page_total > 1:
                    help_embed.title = "Extensions (Page {:,} of {:,})".format(
                        page_count, page_total
                    )

        if len(help_embed.fields):
            if len(ext_list) == 1:
                help_embed.set_footer(text="1 Extension Total")
            else:
                help_embed.set_footer(text="{} Extensions Total".format(len(ext_list)))
            await self._send_embed(ctx, help_embed, to_pm)

    @decorators.command(
        rest_is_raw=True,
        brief="Say a message.",
    )
    @checks.bot_has_perms(manage_messages=True)
    @checks.is_bot_admin()
    async def say(self, ctx, *, content):
        """
        Usage: {0}say
        Output:
            Deletes the command invocation
            and resends the exact content.
        """
        await ctx.message.delete()
        await ctx.send_or_reply(content)

    @decorators.command(
        name="del",
        rest_is_raw=True,
        brief="Delete a message.",
        implemented="2021-05-05 05:12:24.354214",
        updated="2021-05-05 05:12:24.354214",
    )
    @checks.is_bot_admin()
    async def _del(self, ctx, *, msg_id: int):
        """
        Usage {0}del
        Output:
            Delete a specific message ID.
        """
        # Ever accidentally have the bot post a password
        # in a server you can't delete it's message in?
        msg = ctx.channel.get_partial_message(msg_id)
        try:
            await msg.delete()
        except Exception as e:
            ctx.author.send(f"{self.bot.emote_dict['failed']} {e}")
            return
        await ctx.react(self.bot.emote_dict["success"])

    @decorators.command(brief="Show shared servers.")
    @checks.is_bot_admin()
    @checks.bot_has_perms(add_reactions=True, external_emojis=True)
    async def sss(self, ctx, user: converters.DiscordUser = None):
        """
        Usage: {0}sss [user]
        Output:
            Servers that the user shares
            with the bot.
        Notes:
            This supplies a more verbose
            output than the public ss command.
            Will default to you if no user is
            specified.
        """
        user = user or ctx.author

        shared = user.mutual_guilds

        width = max([len(str(x.name)) for x in shared])
        formatted = "\n".join([f"{str(x.name).ljust(width)} : {x.id}" for x in shared])
        p = pagination.MainMenu(
            pagination.TextPageSource(formatted, prefix="```fix", max_size=500)
        )
        await ctx.send_or_reply(f"** I share {len(shared)} servers with `{user}`**")
        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    @decorators.command(
        aliases=["epost"],
        brief="Sends all server emojis.",
        implemented="2021-05-10 20:14:33.223405",
        updated="2021-05-10 20:14:33.223405",
    )
    @checks.has_perms(manage_emojis=True)
    @checks.cooldown()
    async def emojipost(self, ctx, dm: converters.Flag = None):
        """
        Usage: {0}emojipost [nodm]
        Alias: {0}epost
        Output:
            Sends a formatted list of
            emojis and their IDs.
            Specify the nodm bool argument
            to avoid the bot from DMing you.
        """
        emojis = sorted(
            [e for e in ctx.guild.emojis if len(e.roles) == 0 and e.available],
            key=lambda e: e.name.lower(),
        )
        paginator = commands.Paginator(suffix="", prefix="")

        for emoji in emojis:
            paginator.add_line(f"{emoji} ➔ `{emoji}`")

        for page in paginator.pages:
            if dm:
                try:
                    await ctx.author.send(page)
                except Exception:
                    await ctx.send_or_reply(page)
            else:
                await ctx.send_or_reply(page)

    @decorators.command(
        aliases=["guildinfo", "gi"],
        brief="Get stats on a bot server.",
        implemented="2021-03-17 22:48:40.720122",
        updated="2021-05-06 00:57:41.774846",
    )
    @checks.bot_has_perms(add_reactions=True, embed_links=True, external_emojis=True)
    @checks.is_bot_admin()
    async def guild(self, ctx, *, argument=None):
        """
        Usage: {0}guild [server]
        Alias: {0}guildinfo, {0}gi
        Output:
            Lists some info about the
            current or specified server.
        """
        if not argument:
            guild = ctx.guild
        options = await converters.BotServer().convert(ctx, argument)
        option_dict = dict()
        if isinstance(options, list):
            for x in options:
                option_dict[x.name] = x
            guild, message = await helpers.choose(ctx, argument, option_dict)
            if not guild:
                return
        else:
            message = None
            guild = options
        server_embed = discord.Embed(color=self.bot.mode.EMBED_COLOR)
        server_embed.title = guild.name

        # Get localized user time
        local_time = discord.utils.utcnow()
        # local_time = UserTime.getUserTime(ctx.author, self.settings, guild.created_at)
        time_str = "{}".format(local_time)

        server_embed.description = "Created at {}".format(time_str)
        online_members = 0
        bot_member = 0
        bot_online = 0
        for member in guild.members:
            if member.bot:
                bot_member += 1
                if not member.status == discord.Status.offline:
                    bot_online += 1
                continue
            if not member.status == discord.Status.offline:
                online_members += 1
        # bot_percent = "{:,g}%".format((bot_member/len(guild.members))*100)
        try:
            rounded = round(
                (online_members / (len(guild.members) - bot_member) * 100), 2
            )
        except ZeroDivisionError:
            rounded = 0
        user_string = "{:,}/{:,} online ({:,g}%)".format(
            online_members, len(guild.members) - bot_member, rounded
        )
        b_string = "bot" if bot_member == 1 else "bots"
        user_string += "\n{:,}/{:,} {} online ({:,g}%)".format(
            bot_online, bot_member, b_string, round((bot_online / bot_member) * 100, 2)
        )
        # server_embed.add_field(name="Members", value="{:,}/{:,} online ({:.2f}%)\n{:,} {} ({}%)".format(online_members, len(guild.members), bot_percent), inline=True)
        server_embed.add_field(
            name="Members ({:,} total)".format(len(guild.members)),
            value=user_string,
            inline=True,
        )
        server_embed.add_field(name="Roles", value=str(len(guild.roles)), inline=False)
        chandesc = "{:,} text, {:,} voice".format(
            len(guild.text_channels), len(guild.voice_channels)
        )
        server_embed.add_field(name="Channels", value=chandesc, inline=False)
        server_embed.add_field(
            name="Owner",
            value=guild.owner.name + "#" + guild.owner.discriminator,
            inline=True,
        )
        server_embed.add_field(
            name="AFK Channel", value=guild.afk_channel, inline=False
        )
        server_embed.add_field(
            name="Verification", value=guild.verification_level, inline=False
        )
        server_embed.add_field(name="Voice Region", value=guild.region, inline=False)
        # server_embed.add_field(name="Shard ID", value="{}/{}".format(guild.shard_id+1, self.bot.shard_count), inline=False)
        server_embed.add_field(
            name="Nitro Boosts",
            value="{} (level {})".format(
                guild.premium_subscription_count, guild.premium_tier
            ),
            inline=False,
        )
        # Find out where in our join position this server is
        joinedList = []
        popList = []
        for g in self.bot.guilds:
            joinedList.append({"ID": g.id, "Joined": g.me.joined_at})
            popList.append({"ID": g.id, "Population": len(g.members)})

        # sort the guilds by join date
        joinedList = sorted(
            joinedList,
            key=lambda x: x["Joined"].timestamp() if x["Joined"] is not None else -1,
        )
        popList = sorted(popList, key=lambda x: x["Population"], reverse=True)

        check_item = {"ID": guild.id, "Joined": guild.me.joined_at}
        total = len(joinedList)
        position = joinedList.index(check_item) + 1
        server_embed.add_field(
            name="Join Position",
            value="{:,} of {:,}".format(position, total),
            inline=False,
        )

        # Get our population position
        check_item = {"ID": guild.id, "Population": len(guild.members)}
        total = len(popList)
        position = popList.index(check_item) + 1
        server_embed.add_field(
            name="Population Rank",
            value="{:,} of {:,}".format(position, total),
            inline=False,
        )

        emojitext = ""
        emojifields = []
        disabledemojis = 0
        twitchemojis = 0
        for i, emoji in enumerate(guild.emojis):
            if not emoji.available:
                disabledemojis += 1
                continue
            if emoji.managed:
                twitchemojis += 1
                continue
            emojiMention = "<{}:{}:{}>".format(
                "a" if emoji.animated else "", emoji.name, emoji.id
            )
            test = emojitext + emojiMention
            if len(test) > 1024:
                # TOOO BIIIIIIIIG
                emojifields.append(emojitext)
                emojitext = emojiMention
            else:
                emojitext = emojitext + emojiMention

        if len(emojitext):
            emojifields.append(emojitext)  # Add any leftovers
        if twitchemojis:
            emojifields.append("{:,} managed".format(twitchemojis))
        if disabledemojis:
            emojifields.append(
                "{:,} unavailable".format(disabledemojis)
            )  # Add the disabled if any

        server_embed.set_thumbnail(url=utils.get_icon(guild))
        server_embed.set_footer(text="Server ID: {}".format(guild.id))
        # Let's send all the embeds we need finishing off with extra emojis as
        # needed
        for i, e in enumerate(emojifields):
            name = (
                "Disabled Emojis"
                if e.lower().endswith("unavailable")
                else "Twitch Emojis"
                if e.lower().endswith("managed")
                else "Emojis ({} of {})".format(i + 1, len(emojifields))
            )
            server_embed.add_field(name=name, value=e, inline=False)
            if len(server_embed) > 6000:  # too big
                server_embed.remove_field(len(server_embed.fields) - 1)
                await ctx.send_or_reply(embed=server_embed)
                server_embed = discord.Embed(color=self.bot.mode.EMBED_COLOR)
                server_embed.title = guild.name
                server_embed.set_thumbnail(url=utils.get_icon(guild))
                server_embed.set_footer(text="Server ID: {}".format(guild.id))
                server_embed.description = "Continued Emojis:"
                server_embed.add_field(name=name, value=e, inline=False)
        if len(server_embed.fields):
            if message:
                try:
                    await message.edit(embed=server_embed)
                except BaseException:
                    await ctx.send_or_reply(embed=server_embed)
            else:
                await ctx.send_or_reply(embed=server_embed)

    @decorators.command(brief="Show members for a server.")
    @checks.bot_has_perms(add_reactions=True, embed_links=True, external_emojis=True)
    async def members(self, ctx, *, argument=None):
        """
        Usage: {0}members
        Output: Lists the members of a passed server
        """
        if not argument:
            guild = ctx.guild
        options = await converters.BotServer().convert(ctx, argument)
        option_dict = dict()
        if isinstance(options, list):
            for x in options:
                option_dict[x.name] = x
            guild, message = await helpers.choose(ctx, argument, option_dict)
            if not guild:
                return
        else:
            guild = options
            message = None
        members = guild.members
        member_list = []
        for entity in members:
            member_list.append(
                {
                    "name": entity,
                    "value": "Mention: {}\nID: `{}`\nNickname: {}".format(
                        entity.mention, entity.id, entity.display_name
                    ),
                }
            )
        p = pagination.MainMenu(
            pagination.FieldPageSource(
                entries=[
                    ("{}. {}".format(y + 1, x["name"]), x["value"])
                    for y, x in enumerate(member_list)
                ],
                per_page=10,
                title=f"Member list for **{guild.name}** ({len(members):,} total)",
            )
        )
        if message:
            await message.delete()
        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    async def tabulate_query(self, ctx, query, *args):
        records = await self.bot.cxn.fetch(query, *args)

        if len(records) == 0:
            return await ctx.send_or_reply(content="No results found.")

        headers = list(records[0].keys())
        table = formatting.TabularData()
        table.set_columns(headers)
        table.add_rows(list(r.values()) for r in records)
        render = table.render()

        fmt = f"```\n{render}\n```"
        if len(fmt) > 2000:
            fp = io.BytesIO(fmt.encode("utf-8"))
            await ctx.send_or_reply(
                content="Too many results...",
                file=discord.File(fp, "results.txt"),
            )
        else:
            await ctx.send_or_reply(content=fmt)

    @decorators.group(
        invoke_without_command=True,
        brief="Show command history.",
        case_insensitive=True,
        writer=1010057368287068222,
    )
    @commands.is_owner()
    async def command_history(self, ctx):
        """
        Usage: {0}command_history <option> [args]
        Output:
            Recorded command history matching
            the specified arguments.
        Options:
            command, server,
            user, log, cog
        """
        query = """SELECT
                        CASE failed
                            WHEN TRUE THEN command || ' [!]'
                            ELSE command
                        END AS "command",
                        to_char(timestamp, 'Mon DD HH12:MI:SS AM') AS "invoked",
                        author_id,
                        server_id
                   FROM commands
                   ORDER BY timestamp DESC
                   LIMIT 15;
                """
        await self.tabulate_query(ctx, query)

    @command_history.command(name="command", aliases=["for"])
    @commands.is_owner()
    async def command_history_for(
        self, ctx, days: typing.Optional[int] = 7, *, command: str
    ):
        """Command history for a command."""

        query = """SELECT *, t.success + t.failed AS "total"
                   FROM (
                       SELECT server_id,
                              SUM(CASE WHEN failed THEN 0 ELSE 1 END) AS "success",
                              SUM(CASE WHEN failed THEN 1 ELSE 0 END) AS "failed"
                       FROM commands
                       WHERE command=$1
                       AND timestamp > (CURRENT_TIMESTAMP - $2::interval)
                       GROUP BY server_id
                   ) AS t
                   ORDER BY "total" DESC
                   LIMIT 30;
                """

        await self.tabulate_query(ctx, query, command, datetime.timedelta(days=days))

    @command_history.command(name="guild", aliases=["server"])
    @commands.is_owner()
    async def command_history_guild(self, ctx, server_id: int):
        """Command history for a guild."""

        query = """SELECT
                        CASE failed
                            WHEN TRUE THEN command || ' [!]'
                            ELSE command
                        END AS "command",
                        channel_id,
                        author_id,
                        timestamp
                   FROM commands
                   WHERE server_id=$1
                   ORDER BY timestamp DESC
                   LIMIT 15;
                """
        await self.tabulate_query(ctx, query, server_id)

    @command_history.command(name="user", aliases=["member"])
    @commands.is_owner()
    async def command_history_user(self, ctx, user_id: int):
        """Command history for a user."""

        query = """SELECT
                        CASE failed
                            WHEN TRUE THEN command || ' [!]'
                            ELSE command
                        END AS "command",
                        server_id,
                        timestamp
                   FROM commands
                   WHERE author_id=$1
                   ORDER BY timestamp DESC
                   LIMIT 20;
                """
        await self.tabulate_query(ctx, query, user_id)

    @command_history.command(name="log")
    @commands.is_owner()
    async def command_history_log(self, ctx, days=7):
        """Command history log for the last N days."""

        query = """SELECT command, COUNT(*)
                   FROM commands
                   WHERE timestamp > (CURRENT_TIMESTAMP - $1::interval)
                   GROUP BY command
                   ORDER BY 2 DESC
                """

        all_commands = {c.qualified_name: 0 for c in self.bot.walk_commands()}

        records = await self.bot.cxn.fetch(query, datetime.timedelta(days=days))
        for name, uses in records:
            if name in all_commands:
                all_commands[name] = uses

        as_data = sorted(all_commands.items(), key=lambda t: t[1], reverse=True)
        table = formatting.TabularData()
        table.set_columns(["Command", "Uses"])
        table.add_rows(tup for tup in as_data)
        render = table.render()

        embed = discord.Embed(title="Summary", color=self.bot.mode.EMBED_COLOR)
        embed.set_footer(
            text="Since"
        ).timestamp = datetime.datetime.utcnow() - datetime.timedelta(days=days)

        top_ten = "\n".join(f"{command}: {uses}" for command, uses in records[:10])
        bottom_ten = "\n".join(f"{command}: {uses}" for command, uses in records[-10:])
        embed.add_field(name="Top 10", value=top_ten)
        embed.add_field(name="Bottom 10", value=bottom_ten)

        unused = ", ".join(name for name, uses in as_data if uses == 0)
        if len(unused) > 1024:
            unused = "Way too many..."

        embed.add_field(name="Unused", value=unused, inline=False)

        await ctx.send_or_reply(
            embed=embed,
            file=discord.File(io.BytesIO(render.encode()), filename="full_results.txt"),
        )

    @command_history.command(name="cog")
    @commands.is_owner()
    async def command_history_cog(
        self, ctx, days: typing.Optional[int] = 7, *, cog: str = None
    ):
        """Command history for a cog or grouped by a cog."""

        interval = datetime.timedelta(days=days)
        if cog is not None:
            cog = self.bot.get_cog(cog)
            if cog is None:
                return await ctx.send_or_reply(content=f"Unknown cog: {cog}")

            query = """SELECT *, t.success + t.failed AS "total"
                       FROM (
                           SELECT command,
                                  SUM(CASE WHEN failed THEN 0 ELSE 1 END) AS "success",
                                  SUM(CASE WHEN failed THEN 1 ELSE 0 END) AS "failed"
                           FROM commands
                           WHERE command = any($1::text[])
                           AND timestamp > (CURRENT_TIMESTAMP - $2::interval)
                           GROUP BY command
                       ) AS t
                       ORDER BY "total" DESC
                       LIMIT 30;
                    """
            return await self.tabulate_query(
                ctx, query, [c.qualified_name for c in cog.walk_commands()], interval
            )

        # A more manual query with a manual grouper.
        query = """SELECT *, t.success + t.failed AS "total"
                   FROM (
                       SELECT command,
                              SUM(CASE WHEN failed THEN 0 ELSE 1 END) AS "success",
                              SUM(CASE WHEN failed THEN 1 ELSE 0 END) AS "failed"
                       FROM commands
                       WHERE timestamp > (CURRENT_TIMESTAMP - $1::interval)
                       GROUP BY command
                   ) AS t;
                """

        class Count:
            __slots__ = ("success", "failed", "total")

            def __init__(self):
                self.success = 0
                self.failed = 0
                self.total = 0

            def add(self, record):
                self.success += record["success"]
                self.failed += record["failed"]
                self.total += record["total"]

        data = defaultdict(Count)
        records = await self.bot.cxn.fetch(query, interval)
        for record in records:
            command = self.bot.get_command(record["command"])
            if command is None or command.cog is None:
                data["No Cog"].add(record)
            else:
                data[command.cog.qualified_name].add(record)

        table = formatting.TabularData()
        table.set_columns(["Cog", "Success", "Failed", "Total"])
        data = sorted(
            [(cog, e.success, e.failed, e.total) for cog, e in data.items()],
            key=lambda t: t[-1],
            reverse=True,
        )

        table.add_rows(data)
        render = table.render()
        await ctx.safe_send(f"```\n{render}\n```")

    @decorators.group(name="noprefix", aliases=["nopfx"], brief="Manage no-prefix users")
    async def noprefix(self, ctx):
        """
        Usage: {0}noprefix <subcommand>
        Aliases: {0}nopfx
        Permission: Bot Admin
        Output: Manage users who can use commands without prefix
        """
        if ctx.invoked_subcommand is None:
            await ctx.send_help(ctx.command)

    @noprefix.command(name="add", brief="Add a user to no-prefix list")
    async def np_add(self, ctx, user: discord.User):
        """
        Usage: {0}noprefix add <user>
        Permission: Bot Admin
        Output: Add a user to the no-prefix list with duration selection
        """
        # Check if bot has noprefix manager
        if not hasattr(self.bot, 'noprefix_manager'):
            return await ctx.send_or_reply("❌ No-prefix system is not available.")

        # Check if user already has no-prefix
        if await self.bot.noprefix_manager.is_user_noprefix(user.id):
            embed = discord.Embed(
                title="❌ Already Added",
                description=f"**{user}** is already in the no-prefix list.",
                color=0xFF0000
            )
            return await ctx.send_or_reply(embed=embed)

        # Show duration selection
        view = DurationSelectView(user, ctx.author)
        embed = discord.Embed(
            title="Select No Prefix Duration",
            description=f"Choose how long **{user}** should have no-prefix permissions:",
            color=self.bot.mode.EMBED_COLOR
        )
        embed.set_thumbnail(url=user.display_avatar.url)

        await ctx.send_or_reply(embed=embed, view=view)

    @noprefix.command(name="remove", brief="Remove a user from no-prefix list")
    async def np_remove(self, ctx, user: discord.User):
        """
        Usage: {0}noprefix remove <user>
        Permission: Bot Admin
        Output: Remove a user from the no-prefix list
        """
        # Check if bot has noprefix manager
        if not hasattr(self.bot, 'noprefix_manager'):
            return await ctx.send_or_reply("❌ No-prefix system is not available.")

        success = await self.bot.noprefix_manager.remove_user(user.id)

        if not success:
            embed = discord.Embed(
                title="❌ Not Found",
                description=f"**{user}** is not in the no-prefix list.",
                color=0xFF0000
            )
            return await ctx.send_or_reply(embed=embed)

        embed = discord.Embed(
            title="✅ Removed No Prefix",
            description=(
                f"**User**: {user.mention} (`{user.id}`)\n"
                f"**Removed By**: {ctx.author.mention}"
            ),
            color=0x00FF00
        )
        embed.set_thumbnail(url=user.display_avatar.url)

        await ctx.send_or_reply(embed=embed)

        # Log the action
        print(f"[NoPrefix] {ctx.author} ({ctx.author.id}) removed {user} ({user.id}) from no-prefix list.")

        # Send DM to user
        try:
            dm_embed = discord.Embed(
                title="⚠️ No Prefix Removed",
                description=(
                    f"Your no-prefix permissions have been removed by an administrator. "
                    f"You will now need to use a prefix to run commands."
                ),
                color=0xFFAA00
            )
            dm_embed.set_footer(text="Contact support if you believe this is an error")
            await user.send(embed=dm_embed)
        except (discord.Forbidden, discord.HTTPException):
            pass  # User has DMs disabled

    @noprefix.command(name="list", brief="List all no-prefix users")
    async def np_list(self, ctx):
        """
        Usage: {0}noprefix list
        Permission: Bot Admin
        Output: Show all users with no-prefix permissions
        """
        # Check if bot has noprefix manager
        if not hasattr(self.bot, 'noprefix_manager'):
            return await ctx.send_or_reply("❌ No-prefix system is not available.")

        users = await self.bot.noprefix_manager.get_all_users()

        if not users:
            embed = discord.Embed(
                title="No Prefix Users",
                description="No users currently have no-prefix permissions.",
                color=self.bot.mode.EMBED_COLOR
            )
            return await ctx.send_or_reply(embed=embed)

        entries = []
        for i, user_data in enumerate(users, 1):
            user = self.bot.get_user(user_data['user_id'])
            user_str = str(user) if user else f"Unknown User ({user_data['user_id']})"

            expiry_display, _ = format_expiry_time(user_data['expiry_time'])
            entries.append(f"`{i}.` **{user_str}** - Expires: {expiry_display}")

        p = pagination.SimplePages(
            entries,
            per_page=10,
            color=self.bot.mode.EMBED_COLOR
        )
        p.embed.title = f"No Prefix Users ({len(users)} total)"

        try:
            await p.start(ctx)
        except Exception as e:
            await ctx.send_or_reply(f"Error displaying list: {e}")

    @noprefix.command(name="status", brief="Check a user's no-prefix status")
    async def np_status(self, ctx, user: discord.User):
        """
        Usage: {0}noprefix status <user>
        Permission: Bot Admin
        Output: Show detailed no-prefix information for a user
        """
        # Check if bot has noprefix manager
        if not hasattr(self.bot, 'noprefix_manager'):
            return await ctx.send_or_reply("❌ No-prefix system is not available.")

        user_info = await self.bot.noprefix_manager.get_user_info(user.id)

        if not user_info:
            embed = discord.Embed(
                title="❌ Not Found",
                description=f"**{user}** does not have no-prefix permissions.",
                color=0xFF0000
            )
            return await ctx.send_or_reply(embed=embed)

        expiry_display, expiry_timestamp = format_expiry_time(user_info['expiry_time'])
        added_by = self.bot.get_user(user_info['added_by']) if user_info['added_by'] else None

        embed = discord.Embed(
            title="No Prefix Status",
            color=0x00FF00
        )
        embed.add_field(name="User", value=f"{user.mention}\n`{user.id}`", inline=True)
        embed.add_field(name="Expires", value=expiry_display, inline=True)
        embed.add_field(name="Added By", value=str(added_by) if added_by else "Unknown", inline=True)
        embed.add_field(name="Added At", value=f"<t:{int(user_info['added_at'].timestamp())}:F>", inline=False)
        embed.set_thumbnail(url=user.display_avatar.url)

        await ctx.send_or_reply(embed=embed)

    @noprefix.command(name="toggle", brief="Toggle no-prefix status for yourself or others")
    async def np_toggle(self, ctx, user: discord.User = None):
        """
        Usage: {0}noprefix toggle [user]
        Permission: None (for self), Bot Admin (for others)
        Output: Toggle a user's no-prefix permissions on/off
        Notes:
            If no user is specified, toggles your own no-prefix status.
            To toggle for other users, you need Bot Admin permissions.
            If the user has no-prefix permissions, they will be removed.
            If the user doesn't have no-prefix permissions, they will be added with lifetime access.
        """
        # Default to the command user if no user specified
        if user is None:
            user = ctx.author

        # Check permissions - only require bot admin if targeting someone else
        if user.id != ctx.author.id:
            if not await checks.check_botadmin(ctx):
                return await ctx.fail("You need Bot Admin permissions to toggle no-prefix for other users.")

        # Check if user currently has noprefix
        has_noprefix = await self.bot.noprefix_manager.is_user_noprefix(user.id)

        if has_noprefix:
            # Remove noprefix
            await self.bot.noprefix_manager.remove_user(user.id)

            embed = discord.Embed(
                title="No-Prefix Disabled",
                description=f"Removed no-prefix permissions for {user.mention}",
                color=0xFF0000
            )
            embed.add_field(name="User", value=f"{user.mention}\n`{user.id}`", inline=True)
            embed.add_field(name="Action", value="Removed", inline=True)
            embed.add_field(name="Removed By", value=ctx.author.mention, inline=True)
            embed.set_thumbnail(url=user.display_avatar.url)

            # Try to DM the user
            try:
                dm_embed = discord.Embed(
                    title="No-Prefix Access Removed",
                    description="Your no-prefix permissions have been removed.",
                    color=0xFF0000
                )
                dm_embed.add_field(name="Server", value=ctx.guild.name if ctx.guild else "Direct Message", inline=True)
                dm_embed.add_field(name="Removed By", value=str(ctx.author), inline=True)
                dm_embed.set_footer(text="Contact support if you believe this is an error")
                await user.send(embed=dm_embed)
            except (discord.Forbidden, discord.HTTPException):
                pass  # User has DMs disabled
        else:
            # Add noprefix with lifetime access
            await self.bot.noprefix_manager.add_user(user.id, None, ctx.author.id)

            embed = discord.Embed(
                title="No-Prefix Enabled",
                description=f"Added lifetime no-prefix permissions for {user.mention}",
                color=0x00FF00
            )
            embed.add_field(name="User", value=f"{user.mention}\n`{user.id}`", inline=True)
            embed.add_field(name="Duration", value="Lifetime", inline=True)
            embed.add_field(name="Added By", value=ctx.author.mention, inline=True)
            embed.set_thumbnail(url=user.display_avatar.url)

            # Try to DM the user
            try:
                dm_embed = discord.Embed(
                    title="No-Prefix Access Granted",
                    description="You now have lifetime no-prefix permissions! You can use commands without any prefix.",
                    color=0x00FF00
                )
                dm_embed.add_field(name="Server", value=ctx.guild.name if ctx.guild else "Direct Message", inline=True)
                dm_embed.add_field(name="Duration", value="Lifetime", inline=True)
                dm_embed.add_field(name="Added By", value=str(ctx.author), inline=True)
                dm_embed.set_footer(text="Use commands without any prefix - just type the command name!")
                await user.send(embed=dm_embed)
            except (discord.Forbidden, discord.HTTPException):
                pass  # User has DMs disabled

        await ctx.send_or_reply(embed=embed)

    @decorators.command(name="noprefixme", aliases=["npme"], brief="Toggle your own no-prefix status")
    async def noprefix_me(self, ctx):
        """
        Usage: {0}noprefixme
        Aliases: {0}npme
        Permission: None
        Output: Toggle your own no-prefix permissions on/off
        Notes:
            This is a shortcut to toggle your own no-prefix status without needing the group command.
        """
        user = ctx.author

        # Check if user currently has noprefix
        has_noprefix = await self.bot.noprefix_manager.is_user_noprefix(user.id)

        if has_noprefix:
            # Remove noprefix
            await self.bot.noprefix_manager.remove_user(user.id)

            embed = discord.Embed(
                title="No-Prefix Disabled",
                description=f"Removed your no-prefix permissions",
                color=0xFF0000
            )
            embed.add_field(name="Status", value="You now need to use prefixes for commands", inline=False)
            embed.set_thumbnail(url=user.display_avatar.url)
        else:
            # Add noprefix with lifetime access
            await self.bot.noprefix_manager.add_user(user.id, None, user.id)

            embed = discord.Embed(
                title="No-Prefix Enabled",
                description=f"Added lifetime no-prefix permissions for you",
                color=0x00FF00
            )
            embed.add_field(name="Status", value="You can now use commands without any prefix!", inline=False)
            embed.set_thumbnail(url=user.display_avatar.url)

        await ctx.send_or_reply(embed=embed)
